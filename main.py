"""
金融股指期货监控系统主程序
"""
import asyncio
import logging
import sys
import os
from datetime import datetime
import pandas as pd
import warnings

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.models.data_models import MonitoringConfig
from src.data_providers.efinance_provider import EfinanceProvider
from src.data_providers.akshare_provider import AKShareProvider
from src.data_providers.index_provider import IndexDataProvider
from src.utils.trading_calendar import TradingCalendar
from src.calculators.basis_calculator import BasisCalculator
from src.calculators.spread_calculator import Spread<PERSON>alculator
from src.analyzers.cost_matrix import CostMatrixGenerator

pd.set_option('display.unicode.ambiguous_as_wide', True)
pd.set_option('display.unicode.east_asian_width', True)
pd.set_option('display.width', 180)
warnings.filterwarnings('ignore') 



class FuturesMonitoringSystem:
    """期货监控系统主类"""
    
    def __init__(self, config: MonitoringConfig):
        self.config = config
        self.logger = self._setup_logging()

        # 初始化组件
        self.trading_calendar = TradingCalendar()

        # 根据配置选择数据提供者
        data_source = getattr(config, 'data_source', 'efinance')
        if data_source == 'akshare':
            self.futures_provider = AKShareProvider()
            self.logger.info("使用AKShare作为期货数据源")
        else:
            self.futures_provider = EfinanceProvider(trading_calendar=self.trading_calendar)
            self.logger.info("使用efinance作为期货数据源")

        self.index_provider = IndexDataProvider()
        self.basis_calculator = BasisCalculator(self.trading_calendar, config.trading_days_per_year)
        self.spread_calculator = SpreadCalculator(self.trading_calendar, config.trading_days_per_year)
        self.cost_matrix_generator = CostMatrixGenerator()

        self.is_running = False
    
    def _setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=getattr(logging, self.config.log_level),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler('futures_monitor.log', encoding='utf-8')
            ]
        )
        return logging.getLogger(__name__)
    
    async def start(self):
        """启动监控系统"""
        self.is_running = True
        self.logger.info("=== 金融股指期货监控系统启动 ===")
        
        try:
            # 验证数据源
            await self._validate_data_sources()
            
            # 主监控循环
            await self._monitoring_loop()
            
        except KeyboardInterrupt:
            self.logger.info("收到停止信号")
        except Exception as e:
            self.logger.error(f"系统运行错误: {e}")
        finally:
            await self.stop()
    
    async def stop(self):
        """停止监控系统"""
        self.is_running = False
        self.logger.info("正在停止监控系统...")
        
        # 关闭资源
        if hasattr(self.futures_provider, 'close'):
            await self.futures_provider.close()
        elif hasattr(self.futures_provider, 'cleanup'):
            await self.futures_provider.cleanup()

        await self.index_provider.close()
        await self.trading_calendar.close()
        
        self.logger.info("监控系统已停止")
    
    async def _validate_data_sources(self):
        """验证数据源可用性"""
        self.logger.info("验证数据源可用性...")
        
        # 验证指数数据源
        status = await self.index_provider.validate_data_sources()
        self.logger.info(f"数据源状态: {status}")
        
        if not any(status.values()):
            raise Exception("所有数据源都不可用")
    
    async def _monitoring_loop(self):
        """主监控循环"""
        while self.is_running:
            try:
                self.logger.info(f"开始数据更新 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                
                # 1. 一次性获取期货合约信息和行情数据（优化后的方法）
                product_codes = list(self.config.futures_mapping.keys())
                contracts, futures_quotes = await self.futures_provider.get_all_futures_data(product_codes)

                if not contracts or not futures_quotes:
                    self.logger.warning("未获取到期货合约信息或行情数据")
                    await asyncio.sleep(60)  # 增加等待时间，减少API压力
                    continue

                self.logger.info(f"获取到 {len(contracts)} 个期货合约和 {len(futures_quotes)} 个行情")
                
                # 3. 获取指数行情
                index_codes = list(self.config.futures_mapping.values())
                index_quotes = await self.index_provider.get_index_quotes(index_codes)
                
                if not index_quotes:
                    self.logger.warning("未获取到指数行情")
                    await asyncio.sleep(30)
                    continue
                
                # 4. 计算基差成本
                basis_costs = await self.basis_calculator.calculate_multiple_basis(
                    futures_quotes, index_quotes, contracts, self.config.futures_mapping
                )
                
                # 5. 计算价差成本
                spread_costs = await self.spread_calculator.calculate_all_spreads(
                    futures_quotes, contracts
                )

                # 6. 计算基差价差成本
                basis_spread_costs = await self.spread_calculator.calculate_basis_spread_costs(
                    basis_costs, contracts
                )

                # 7. 生成成本矩阵
                cost_matrix = self.cost_matrix_generator.generate_cost_matrix(
                    basis_costs, spread_costs, contracts
                )

                # 8. 显示结果
                await self._display_results(cost_matrix, contracts, basis_spread_costs)
                
                # 8. 等待下次更新
                self.logger.info(f"等待 {self.config.update_interval} 秒后下次更新...")
                await asyncio.sleep(self.config.update_interval)
                
            except Exception as e:
                self.logger.error(f"监控循环错误: {e}")
                await asyncio.sleep(10)
    
    async def _display_results(self, cost_matrix, contracts, basis_spread_costs=None):
        """显示结果"""
        print("\n" + "="*120)
        print(f"金融股指期货监控面板 - 更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("="*120)

        # 显示综合成本矩阵（合并基差成本和基差价差成本）
        if cost_matrix.basis_costs and basis_spread_costs:
            print("\n【综合成本矩阵】")
            comprehensive_df = self.cost_matrix_generator.create_comprehensive_cost_matrix(
                cost_matrix.basis_costs, basis_spread_costs, contracts
            )

            if not comprehensive_df.empty:
                print(comprehensive_df.to_string(index=False))
            else:
                print("暂无综合成本数据")

        # 显示基差成本矩阵（单独显示）
        elif cost_matrix.basis_costs:
            print("\n【基差成本矩阵】")
            basis_df = self.cost_matrix_generator.create_basis_matrix_dataframe(
                cost_matrix.basis_costs, contracts
            )

            # 添加合约详细信息
            if not basis_df.empty:
                enhanced_data = []
                for _, row in basis_df.iterrows():
                    contract_code = row['合约代码']

                    # 获取合约详细信息
                    contract = None
                    for c in contracts:
                        if c.contract_code == contract_code:
                            contract = c
                            break

                    # 获取到期日期
                    expiry_str = ""
                    if contract and contract.expiry_date:
                        expiry_str = contract.expiry_date.strftime("%m-%d")

                    # 获取合约名称（简化显示）
                    contract_name = ""
                    if contract and contract.contract_name:
                        # 提取关键信息
                        if "中证500" in contract.contract_name:
                            contract_name = "中证500"
                        elif "中证1000" in contract.contract_name:
                            contract_name = "中证1000"
                        elif "沪深300" in contract.contract_name:
                            contract_name = "沪深300"
                        elif "上证50" in contract.contract_name:
                            contract_name = "上证50"
                        else:
                            contract_name = contract.contract_name[:6]

                    enhanced_row = row.to_dict()
                    enhanced_row['合约名称'] = contract_name
                    enhanced_row['到期日期'] = expiry_str

                    enhanced_data.append(enhanced_row)

                # 重新排列列顺序
                enhanced_df = pd.DataFrame(enhanced_data)
                column_order = [
                    '品种', '合约代码', '合约名称', '合约月份', '是否主力',
                    '期货价格', '现货价格', '基差', '基差率(%)',
                    '剩余天数', '年化成本(%)', '到期日期', '更新时间'
                ]

                # 确保所有列都存在
                for col in column_order:
                    if col not in enhanced_df.columns:
                        enhanced_df[col] = ""

                enhanced_df = enhanced_df[column_order]
                print(enhanced_df.to_string(index=False))

                # 显示统计信息
                print(f"\n统计信息:")
                print(f"总合约数: {len(enhanced_df)}")

                # 按品种统计
                product_stats = enhanced_df.groupby('品种').size()
                for product, count in product_stats.items():
                    main_count = len(enhanced_df[(enhanced_df['品种'] == product) & (enhanced_df['是否主力'] == '是')])
                    print(f"{product}: {count}个合约 (主力: {main_count}个)")

                # 显示价格异常提醒
                print(f"\n价格检查:")
                abnormal_count = 0
                for _, row in enhanced_df.iterrows():
                    try:
                        futures_price = float(row['期货价格'])
                        spot_price = float(row['现货价格'])

                        # 检查价格是否合理（期货价格应该在现货价格的合理范围内）
                        if spot_price > 0:
                            price_ratio = futures_price / spot_price
                            if price_ratio < 0.8 or price_ratio > 1.2:
                                print(f"⚠️  {row['合约代码']}: 期货价格 {futures_price:.2f} vs 现货价格 {spot_price:.2f} (比率: {price_ratio:.3f})")
                                abnormal_count += 1
                    except (ValueError, TypeError):
                        continue

                if abnormal_count == 0:
                    print("✅ 所有合约价格正常")
            else:
                print("暂无基差数据")

        # 显示价差成本矩阵
        if cost_matrix.spread_costs:
            print("\n【价差成本矩阵】")
            spread_df = self.cost_matrix_generator.create_spread_matrix_dataframe(
                cost_matrix.spread_costs, contracts
            )
            print(spread_df.to_string(index=False))

        # 显示汇总统计
        summary = self.cost_matrix_generator.generate_summary_statistics(cost_matrix)
        print(f"\n【汇总统计】")
        print(f"基差机会数量: {summary['overall_summary']['basis_opportunities']}")
        print(f"价差机会数量: {summary['overall_summary']['spread_opportunities']}")

        if summary['basis_summary']:
            print(f"平均基差率: {summary['basis_summary']['avg_basis_rate']:.2f}%")
            print(f"平均年化成本: {summary['basis_summary']['avg_annualized_cost']:.2f}%")

        if summary['spread_summary']:
            print(f"平均价差率: {summary['spread_summary']['avg_spread_rate']:.2f}%")
            print(f"平均时间成本: {summary['spread_summary']['avg_time_cost']:.2f}%")


async def main():
    """主函数"""
    # 创建配置
    config = MonitoringConfig(
        futures_mapping={
            'IC': 'sh000905',  # 中证500股指期货 -> 中证500指数
            'IM': 'sh000852',  # 中证1000股指期货 -> 中证1000指数
            'IF': 'sh000300',  # 沪深300股指期货 -> 沪深300指数
            'IH': 'sh000016',  # 上证50股指期货 -> 上证50指数
        },
        trading_days_per_year=243,
        update_interval=90,  # 90秒更新一次，减少API调用频率
        log_level="INFO",
        data_source="akshare",  # 使用AKShare作为数据源
        enable_fallback=True    # 启用回退机制
    )
    
    # 创建并启动监控系统
    system = FuturesMonitoringSystem(config)
    await system.start()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n程序已停止")
    except Exception as e:
        print(f"程序运行错误: {e}")
        import traceback
        traceback.print_exc()
