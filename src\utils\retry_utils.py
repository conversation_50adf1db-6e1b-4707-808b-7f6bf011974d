"""
重试工具模块
提供统一的重试机制和配置
"""
import asyncio
import logging
from typing import Callable, Any


class RetryConfig:
    """重试配置"""

    def __init__(self, max_retries: int = 3, base_delay: float = 1.0, max_delay: float = 60.0, backoff_factor: float = 2.0):
        """
        初始化重试配置
        
        Args:
            max_retries: 最大重试次数
            base_delay: 基础延迟时间（秒）
            max_delay: 最大延迟时间（秒）
            backoff_factor: 退避因子
        """
        self.max_retries = max_retries
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.backoff_factor = backoff_factor

    def get_delay(self, attempt: int) -> float:
        """计算重试延迟时间（指数退避）"""
        delay = self.base_delay * (self.backoff_factor ** attempt)
        return min(delay, self.max_delay)


async def retry_with_backoff(func: Callable, retry_config: RetryConfig, logger: logging.Logger, operation_name: str) -> Any:
    """
    带重试的异步函数执行器
    
    Args:
        func: 要执行的异步函数
        retry_config: 重试配置
        logger: 日志记录器
        operation_name: 操作名称（用于日志）
        
    Returns:
        函数执行结果
        
    Raises:
        最后一次执行的异常
    """
    last_exception = None

    for attempt in range(retry_config.max_retries + 1):
        try:
            return await func()
        except Exception as e:
            last_exception = e

            # 检查是否是可重试的错误
            error_msg = str(e).lower()
            is_retryable = any(keyword in error_msg for keyword in [
                'connection', 'timeout', 'network', 'remote end closed',
                'max retries exceeded', 'protocol error', 'read timeout',
                'connect timeout', 'ssl', 'certificate', 'handshake'
            ])

            if attempt < retry_config.max_retries and is_retryable:
                delay = retry_config.get_delay(attempt)
                logger.warning(f"{operation_name} 失败 (尝试 {attempt + 1}/{retry_config.max_retries + 1}): {e}")
                logger.info(f"等待 {delay:.1f} 秒后重试...")
                await asyncio.sleep(delay)
            else:
                break

    # 所有重试都失败了
    logger.error(f"{operation_name} 最终失败: {last_exception}")
    raise last_exception
