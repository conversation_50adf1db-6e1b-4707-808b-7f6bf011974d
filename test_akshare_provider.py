#!/usr/bin/env python3
"""
测试AKShare数据提供者
验证期货合约信息获取和实时行情数据的正确性
"""

import asyncio
import sys
import os
import logging
from datetime import datetime

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.data_providers.akshare_provider import AKShareProvider


async def test_akshare_provider():
    """测试AKShare数据提供者的各项功能"""
    
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    logger = logging.getLogger(__name__)
    logger.info("开始测试AKShare数据提供者")
    
    # 创建提供者实例
    provider = AKShareProvider()
    
    try:
        # 测试1: 获取期货合约信息
        logger.info("=" * 60)
        logger.info("测试1: 获取期货合约信息")
        logger.info("=" * 60)
        
        product_codes = ['IC', 'IF', 'IH', 'IM']
        contracts = await provider.get_futures_contracts(product_codes)
        
        if contracts:
            logger.info(f"成功获取 {len(contracts)} 个期货合约")
            for contract in contracts:
                logger.info(f"合约: {contract.contract_code} - {contract.contract_name}")
                logger.info(f"  品种: {contract.product_code}")
                logger.info(f"  到期日: {contract.expiry_date}")
                logger.info(f"  是否主力: {contract.is_main_contract}")
                logger.info(f"  成交量: {contract.volume}")
                logger.info(f"  成交量占比: {contract.volume_ratio:.2f}%")
                logger.info("-" * 40)
        else:
            logger.error("未获取到期货合约信息")
        
        # 测试2: 获取期货行情数据
        logger.info("=" * 60)
        logger.info("测试2: 获取期货行情数据")
        logger.info("=" * 60)
        
        if contracts:
            # 先直接获取行情数据看看结构
            quotes_data = await provider._get_futures_realtime_quotes()
            if quotes_data is not None:
                logger.info(f"行情数据结构: {quotes_data.columns.tolist()}")
                logger.info(f"行情数据样例:\n{quotes_data.head()}")

            quotes = await provider.get_futures_quotes(contracts)

            if quotes:
                logger.info(f"成功获取 {len(quotes)} 个合约的行情数据")
                for contract_code, quote in quotes.items():
                    logger.info(f"行情: {contract_code}")
                    logger.info(f"  当前价格: {quote.current_price}")
                    logger.info(f"  开盘价: {quote.open_price}")
                    logger.info(f"  最高价: {quote.high_price}")
                    logger.info(f"  最低价: {quote.low_price}")
                    logger.info(f"  成交量: {quote.volume}")
                    logger.info(f"  成交额: {quote.amount}")
                    logger.info(f"  昨收价: {quote.prev_close}")
                    logger.info(f"  涨跌幅: {quote.change_pct:.2f}%")
                    logger.info("-" * 40)
            else:
                logger.error("未获取到期货行情数据")
        
        # 测试3: 一次性获取合约和行情数据
        logger.info("=" * 60)
        logger.info("测试3: 一次性获取合约和行情数据")
        logger.info("=" * 60)
        
        contracts_all, quotes_all = await provider.get_all_futures_data(product_codes)
        
        logger.info(f"一次性获取: {len(contracts_all)} 个合约, {len(quotes_all)} 个行情")
        
        # 验证数据一致性
        for contract in contracts_all:
            if contract.contract_code in quotes_all:
                quote = quotes_all[contract.contract_code]
                logger.info(f"✓ {contract.contract_code}: 合约和行情数据匹配")
                logger.info(f"  合约成交量: {contract.volume}, 行情成交量: {quote.volume}")
            else:
                logger.warning(f"✗ {contract.contract_code}: 缺少行情数据")
        
        # 测试4: 获取交易日历
        logger.info("=" * 60)
        logger.info("测试4: 获取交易日历")
        logger.info("=" * 60)
        
        start_date = "20250101"
        end_date = "20250131"
        trading_days = await provider.get_trading_days(start_date, end_date)
        
        logger.info(f"获取 {start_date} 到 {end_date} 的交易日: {len(trading_days)} 天")
        logger.info(f"交易日列表: {trading_days[:10]}...")  # 只显示前10天
        
        # 测试5: 数据质量检查
        logger.info("=" * 60)
        logger.info("测试5: 数据质量检查")
        logger.info("=" * 60)
        
        quality_issues = []
        
        for contract in contracts_all:
            # 检查合约代码格式
            if not contract.contract_code or len(contract.contract_code) < 6:
                quality_issues.append(f"合约代码格式异常: {contract.contract_code}")
            
            # 检查到期日期
            if not contract.expiry_date:
                quality_issues.append(f"缺少到期日期: {contract.contract_code}")
            elif contract.expiry_date < datetime.now().date():
                quality_issues.append(f"到期日期已过期: {contract.contract_code} - {contract.expiry_date}")
            
            # 检查行情数据
            if contract.contract_code in quotes_all:
                quote = quotes_all[contract.contract_code]
                if quote.current_price <= 0:
                    quality_issues.append(f"价格异常: {contract.contract_code} - {quote.current_price}")
                if quote.volume < 0:
                    quality_issues.append(f"成交量异常: {contract.contract_code} - {quote.volume}")
        
        if quality_issues:
            logger.warning(f"发现 {len(quality_issues)} 个数据质量问题:")
            for issue in quality_issues:
                logger.warning(f"  - {issue}")
        else:
            logger.info("✓ 数据质量检查通过")
        
        # 测试总结
        logger.info("=" * 60)
        logger.info("测试总结")
        logger.info("=" * 60)
        
        logger.info(f"✓ 期货合约获取: {len(contracts)} 个")
        logger.info(f"✓ 期货行情获取: {len(quotes)} 个")
        logger.info(f"✓ 一次性数据获取: {len(contracts_all)} 个合约, {len(quotes_all)} 个行情")
        logger.info(f"✓ 交易日历获取: {len(trading_days)} 天")
        logger.info(f"✓ 数据质量问题: {len(quality_issues)} 个")
        
        if len(contracts_all) > 0 and len(quotes_all) > 0 and len(quality_issues) == 0:
            logger.info("🎉 AKShare数据提供者测试通过!")
        else:
            logger.warning("⚠️  AKShare数据提供者测试存在问题，请检查")
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理资源
        await provider.cleanup()
        logger.info("测试完成，资源已清理")


if __name__ == "__main__":
    try:
        asyncio.run(test_akshare_provider())
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"测试运行错误: {e}")
        import traceback
        traceback.print_exc()
