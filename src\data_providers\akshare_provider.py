#!/usr/bin/env python3
"""
AKShare数据提供者
提供期货基础信息和实时行情数据
"""

import asyncio
import logging
import os
import pickle
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Tuple
import pandas as pd
import akshare as ak
from concurrent.futures import ThreadPoolExecutor

from ..models.data_models import FuturesContract, FuturesQuote, IndexQuote
from ..core.architecture import DataProvider
from ..utils.retry_utils import retry_with_backoff, RetryConfig


class AKShareProvider(DataProvider):
    """AKShare数据提供者"""
    
    def __init__(self, max_workers: int = 4):
        super().__init__()
        self.logger = logging.getLogger(__name__)
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        
        # 重试配置
        self.retry_config = RetryConfig(
            max_retries=3,
            base_delay=1.0,
            max_delay=10.0,
            backoff_factor=2.0
        )
        
        # 缓存期货基础信息
        self._futures_base_info: Optional[pd.DataFrame] = None
        self._last_base_info_update: Optional[datetime] = None
        self._base_info_cache_duration = 86400  # 24小时缓存（一天）
        
        # 本地文件缓存
        self._cache_dir = "cache"
        self._base_info_cache_file = os.path.join(self._cache_dir, "akshare_futures_base_info.pkl")
        
        # 确保缓存目录存在
        if not os.path.exists(self._cache_dir):
            os.makedirs(self._cache_dir)
        
        # 品种代码映射
        self.product_mapping = {
            'IC': '中证500',
            'IF': '沪深300', 
            'IH': '上证50',
            'IM': '中证1000'
        }
    
    async def get_futures_contracts(self, product_codes: List[str] = None) -> List[FuturesContract]:
        """获取期货合约列表"""
        try:
            # 获取期货基础信息
            base_info = await self._get_futures_base_info()
            if base_info is None or base_info.empty:
                self.logger.error("无法获取期货基础信息")
                return []

            self.logger.info(f"获取到期货合约信息，共 {len(base_info)} 条记录")
            self.logger.debug(f"合约信息列名: {list(base_info.columns)}")

            contracts = []

            # 按品种分组处理
            for product_code in (product_codes or ['IC', 'IF', 'IH', 'IM']):
                self.logger.info(f"处理 {product_code} 品种合约")

                # 过滤该品种的合约 - 使用品种列而不是合约代码
                if '品种' in base_info.columns:
                    product_contracts = base_info[
                        base_info['品种'] == product_code
                    ].copy()
                else:
                    # 如果没有品种列，尝试从合约代码筛选
                    product_contracts = base_info[
                        base_info['合约代码'].str.startswith(product_code, na=False)
                    ].copy()

                if product_contracts.empty:
                    self.logger.warning(f"未找到 {product_code} 品种的合约")
                    continue

                self.logger.info(f"找到 {len(product_contracts)} 个 {product_code} 品种的原始合约")

                # 过滤掉期权合约，只保留期货合约
                # 期权合约通常包含 -C- 或 -P- 标识
                futures_contracts = product_contracts[
                    ~product_contracts['合约代码'].str.contains('-[CP]-', na=False, regex=True)
                ].copy()

                if futures_contracts.empty:
                    self.logger.warning(f"过滤期权后，未找到 {product_code} 品种的期货合约")
                    continue

                # 限制每个品种最多4个合约，按合约月份排序
                futures_contracts = futures_contracts.sort_values('合约月份', ascending=True).head(4)

                self.logger.info(f"最终选择 {len(futures_contracts)} 个 {product_code} 期货合约")

                # 创建合约对象
                for idx, row in futures_contracts.iterrows():
                    try:
                        contract_code = str(row['合约代码'])
                        contract_month = str(row['合约月份'])

                        # 解析到期日期
                        expiry_date = self._parse_expiry_date_from_month(contract_month)

                        # 构造合约名称
                        product_name = self.product_mapping.get(product_code, product_code)
                        contract_name = f"{product_name}{contract_month}"

                        contract = FuturesContract(
                            contract_code=contract_code,
                            product_code=product_code,
                            contract_name=contract_name,
                            quote_id=f"akshare.{contract_code}",
                            market_type='中金所',
                            expiry_date=expiry_date,
                            is_main_contract=(idx == futures_contracts.index[0]),  # 第一个为主力合约
                            volume=0,  # 基础信息中没有成交量，后续从行情中获取
                            volume_ratio=0.0
                        )

                        contracts.append(contract)
                        self.logger.debug(f"创建合约: {contract_code} - {contract_name} - 到期: {expiry_date}")

                    except Exception as e:
                        self.logger.warning(f"创建合约失败 {row.get('合约代码', 'Unknown')}: {e}")
                        continue

            self.logger.info(f"总共获取到 {len(contracts)} 个期货合约")
            return contracts

        except Exception as e:
            self.logger.error(f"获取期货合约失败: {e}")
            import traceback
            traceback.print_exc()
            return []
    
    async def get_futures_quotes(self, contracts: List[FuturesContract]) -> Dict[str, FuturesQuote]:
        """获取期货行情数据"""
        try:
            # 获取实时行情数据
            quotes_data = await self._get_futures_realtime_quotes()
            if quotes_data is None or quotes_data.empty:
                self.logger.error("无法获取期货实时行情")
                return {}

            self.logger.info(f"获取到行情数据，共 {len(quotes_data)} 条记录")
            self.logger.debug(f"行情数据列名: {list(quotes_data.columns)}")

            quotes = {}

            # 创建产品代码到中文名称的映射
            product_name_mapping = {
                'IC': '中证500指数期货',
                'IF': '沪深300指数期货',
                'IH': '上证50指数期货',
                'IM': '中证1000指数期货'
            }

            for contract in contracts:
                try:
                    # 查找对应的行情数据 - 使用symbol列匹配
                    contract_data = None

                    # 尝试不同的匹配方式
                    if 'symbol' in quotes_data.columns:
                        # 方法1: 根据产品代码和月份匹配中文名称
                        product_name = product_name_mapping.get(contract.product_code)
                        if product_name:
                            # 从合约代码提取月份，如IC2509 -> 2509
                            if len(contract.contract_code) >= 6:
                                month_part = contract.contract_code[-4:]  # 2509
                                expected_symbol = f"{product_name}{month_part}"

                                contract_data = quotes_data[
                                    quotes_data['symbol'] == expected_symbol
                                ]

                        # 方法2: 如果没找到，尝试模糊匹配产品名称
                        if contract_data.empty and product_name:
                            contract_data = quotes_data[
                                quotes_data['symbol'].str.contains(product_name, na=False, case=False)
                            ]

                            # 如果找到多个，选择第一个（通常是主力合约）
                            if not contract_data.empty:
                                contract_data = contract_data.head(1)

                        # 方法3: 直接尝试匹配合约代码（可能不会成功，但试试）
                        if contract_data.empty:
                            contract_data = quotes_data[
                                quotes_data['symbol'].str.contains(contract.contract_code, na=False, case=False)
                            ]

                    if contract_data is None or contract_data.empty:
                        self.logger.warning(f"未找到合约 {contract.contract_code} 的行情数据")
                        continue

                    row = contract_data.iloc[0]

                    quote = FuturesQuote(
                        contract_code=contract.contract_code,
                        timestamp=datetime.now(),
                        current_price=float(row.get('current_price', 0)),
                        open_price=float(row.get('open', 0)),
                        high_price=float(row.get('high', 0)),
                        low_price=float(row.get('low', 0)),
                        volume=int(row.get('volume', 0)),
                        amount=float(row.get('amount', 0)),
                        prev_close=float(row.get('last_close', 0)),
                        change_pct=0,  # 需要计算
                        change_amount=0  # 需要计算
                    )

                    # 计算涨跌幅和涨跌额
                    if quote.prev_close > 0:
                        quote.change_amount = quote.current_price - quote.prev_close
                        quote.change_pct = (quote.change_amount / quote.prev_close) * 100

                    quotes[contract.contract_code] = quote
                    self.logger.debug(f"获取行情: {contract.contract_code} - 价格: {quote.current_price}")

                except Exception as e:
                    self.logger.warning(f"处理合约 {contract.contract_code} 行情失败: {e}")
                    continue

            self.logger.info(f"获取到 {len(quotes)} 个合约的行情数据")
            return quotes

        except Exception as e:
            self.logger.error(f"获取期货行情失败: {e}")
            return {}
    
    async def _get_futures_base_info(self) -> Optional[pd.DataFrame]:
        """获取期货基础信息（带本地文件缓存）"""
        now = datetime.now()

        # 检查内存缓存是否有效
        if (self._futures_base_info is not None and
            self._last_base_info_update is not None and
            (now - self._last_base_info_update).total_seconds() < self._base_info_cache_duration):
            self.logger.debug("使用内存缓存的期货基础信息")
            return self._futures_base_info

        # 尝试从本地文件加载缓存
        cached_data = await self._load_base_info_from_file()
        if cached_data is not None:
            self._futures_base_info = cached_data
            self._last_base_info_update = now
            self.logger.info("从本地文件加载期货基础信息缓存")
            return cached_data

        # 尝试从API获取新数据
        async def _fetch_base_info():
            self.logger.info("从AKShare API获取期货合约信息...")

            # 使用futures_contract_info_cffex接口获取中金所期货合约信息
            today_str = datetime.now().strftime("%Y%m%d")
            base_info = await asyncio.get_event_loop().run_in_executor(
                self.executor, ak.futures_contract_info_cffex, today_str
            )

            # 保存到内存缓存
            self._futures_base_info = base_info
            self._last_base_info_update = now

            # 保存到本地文件
            await self._save_base_info_to_file(base_info)

            self.logger.info(f"更新期货合约信息缓存并保存到本地文件，获取到 {len(base_info)} 条记录")
            return base_info

        try:
            # 使用重试机制获取数据
            return await retry_with_backoff(
                _fetch_base_info,
                self.retry_config,
                self.logger,
                "获取期货合约信息"
            )

        except Exception as e:
            self.logger.error(f"获取期货合约信息最终失败: {e}")

            # 如果API调用失败，尝试使用旧的本地缓存文件
            old_cached_data = await self._load_base_info_from_file(ignore_expiry=True)
            if old_cached_data is not None:
                self.logger.warning("API调用失败，使用旧的本地缓存文件")
                self._futures_base_info = old_cached_data
                return old_cached_data

            return None
    
    async def _get_futures_realtime_quotes(self) -> Optional[pd.DataFrame]:
        """获取期货实时行情数据"""
        try:
            async def _fetch_quotes():
                # 获取中金所主力合约代码
                cffex_symbols = await asyncio.get_event_loop().run_in_executor(
                    self.executor, ak.match_main_contract, "cffex"
                )

                self.logger.info(f"获取到中金所主力合约: {cffex_symbols}")

                # 获取金融期货实时行情
                quotes_data = await asyncio.get_event_loop().run_in_executor(
                    self.executor, ak.futures_zh_spot, cffex_symbols, "FF", "0"
                )
                return quotes_data

            return await retry_with_backoff(
                _fetch_quotes,
                self.retry_config,
                self.logger,
                "获取期货实时行情"
            )

        except Exception as e:
            self.logger.error(f"获取期货实时行情失败: {e}")
            return None

    async def _parse_contract_info(self, contract_code: str, contract_name: str = None) -> Tuple[date, str]:
        """解析合约信息，获取到期日期和月份"""
        try:
            # 从合约代码中提取年月信息
            # 例如: IC2501 -> 2025年01月
            if len(contract_code) >= 6:
                year_month = contract_code[-4:]  # 取后4位
                year = int("20" + year_month[:2])  # 前两位是年份
                month = int(year_month[2:])  # 后两位是月份

                # 计算到期日期（第三个周五）
                expiry_date = self._calculate_expiry_date(year, month)
                contract_month = f"{year:04d}{month:02d}"

                return expiry_date, contract_month
            else:
                # 如果无法解析，使用默认值
                today = date.today()
                return today, today.strftime("%Y%m")

        except Exception as e:
            self.logger.warning(f"解析合约信息失败 {contract_code}: {e}")
            today = date.today()
            return today, today.strftime("%Y%m")

    def _calculate_expiry_date(self, year: int, month: int) -> date:
        """计算期货合约到期日期（第三个周五）"""
        try:
            # 找到该月第一天
            first_day = date(year, month, 1)

            # 找到第一个周五
            days_until_friday = (4 - first_day.weekday()) % 7
            first_friday = first_day + timedelta(days=days_until_friday)

            # 第三个周五
            third_friday = first_friday + timedelta(days=14)

            # 如果第三个周五超出了当月，则使用当月最后一个周五
            if third_friday.month != month:
                # 找到当月最后一个周五
                last_day = date(year, month + 1, 1) - timedelta(days=1) if month < 12 else date(year, 12, 31)
                days_back_to_friday = (last_day.weekday() - 4) % 7
                third_friday = last_day - timedelta(days=days_back_to_friday)

            return third_friday

        except Exception as e:
            self.logger.warning(f"计算到期日期失败 {year}-{month}: {e}")
            return date(year, month, 15)  # 默认使用月中

    def _parse_expiry_date_from_month(self, contract_month: str) -> Optional[date]:
        """从合约月份解析到期日期"""
        try:
            # 合约月份格式如: 2507, 2412 等
            if len(contract_month) == 4:
                year = int("20" + contract_month[:2])  # 前两位是年份
                month = int(contract_month[2:])  # 后两位是月份

                return self._calculate_expiry_date(year, month)
            else:
                self.logger.warning(f"无法解析合约月份格式: {contract_month}")
                return None

        except Exception as e:
            self.logger.warning(f"解析合约月份失败 {contract_month}: {e}")
            return None

    async def _save_base_info_to_file(self, data: pd.DataFrame) -> None:
        """保存期货基础信息到本地文件"""
        try:
            cache_data = {
                'data': data,
                'timestamp': datetime.now(),
                'version': '1.0'
            }

            with open(self._base_info_cache_file, 'wb') as f:
                pickle.dump(cache_data, f)

            self.logger.debug(f"期货基础信息已保存到: {self._base_info_cache_file}")

        except Exception as e:
            self.logger.warning(f"保存期货基础信息到本地文件失败: {e}")

    async def _load_base_info_from_file(self, ignore_expiry: bool = False) -> Optional[pd.DataFrame]:
        """从本地文件加载期货基础信息"""
        try:
            if not os.path.exists(self._base_info_cache_file):
                return None

            with open(self._base_info_cache_file, 'rb') as f:
                cache_data = pickle.load(f)

            # 检查缓存是否过期
            if not ignore_expiry:
                cache_time = cache_data.get('timestamp', datetime.min)
                now = datetime.now()

                if (now - cache_time).total_seconds() > self._base_info_cache_duration:
                    self.logger.debug("本地缓存文件已过期")
                    return None

            data = cache_data.get('data')
            if data is not None and not data.empty:
                self.logger.debug(f"从本地文件加载期货基础信息: {len(data)} 条记录")
                return data

        except Exception as e:
            self.logger.warning(f"从本地文件加载期货基础信息失败: {e}")

        return None

    async def get_all_futures_data(self, product_codes: List[str]) -> Tuple[List[FuturesContract], Dict[str, FuturesQuote]]:
        """一次性获取期货合约信息和行情数据，保持与现有系统的兼容性"""
        try:
            # 获取合约信息
            contracts = await self.get_futures_contracts(product_codes)
            if not contracts:
                self.logger.warning("未获取到期货合约信息")
                return [], {}

            # 获取行情数据
            quotes = await self.get_futures_quotes(contracts)

            # 更新合约的成交量信息
            total_volume_by_product = {}

            # 按品种计算总成交量
            for contract in contracts:
                if contract.product_code not in total_volume_by_product:
                    total_volume_by_product[contract.product_code] = 0

                if contract.contract_code in quotes:
                    quote = quotes[contract.contract_code]
                    contract.volume = quote.volume
                    total_volume_by_product[contract.product_code] += quote.volume

            # 计算成交量占比
            for contract in contracts:
                total_volume = total_volume_by_product.get(contract.product_code, 0)
                if total_volume > 0:
                    contract.volume_ratio = (contract.volume / total_volume) * 100
                else:
                    contract.volume_ratio = 0.0

            self.logger.info(f"获取到 {len(contracts)} 个期货合约和 {len(quotes)} 个行情")
            return contracts, quotes

        except Exception as e:
            self.logger.error(f"获取期货数据失败: {e}")
            return [], {}

    async def get_index_quotes(self, index_codes: List[str] = None) -> Dict[str, IndexQuote]:
        """获取指数行情（AKShare不直接提供指数数据，返回空字典）"""
        self.logger.info("AKShare提供者不支持指数行情数据")
        return {}

    async def get_trading_days(self, start_date: str, end_date: str) -> List[str]:
        """获取交易日历（简化实现）"""
        try:
            from datetime import datetime, timedelta

            start = datetime.strptime(start_date, '%Y%m%d')
            end = datetime.strptime(end_date, '%Y%m%d')

            trading_days = []
            current = start

            while current <= end:
                # 简单的工作日判断（周一到周五）
                if current.weekday() < 5:  # 0-4 是周一到周五
                    trading_days.append(current.strftime('%Y%m%d'))
                current += timedelta(days=1)

            # 排除一些明显的节假日（简化处理）
            holidays = self._get_simple_holidays(start.year, end.year)
            trading_days = [day for day in trading_days if day not in holidays]

            return trading_days

        except Exception as e:
            self.logger.error(f"获取交易日历失败: {e}")
            return []

    def _get_simple_holidays(self, start_year: int, end_year: int) -> List[str]:
        """获取简单的节假日列表"""
        holidays = []

        for year in range(start_year, end_year + 1):
            # 元旦
            holidays.extend([f"{year}0101"])

            # 春节（简化为2月前两周）
            for day in range(1, 15):
                holidays.append(f"{year}02{day:02d}")

            # 清明节（4月4-6日）
            holidays.extend([f"{year}0404", f"{year}0405", f"{year}0406"])

            # 劳动节（5月1-3日）
            holidays.extend([f"{year}0501", f"{year}0502", f"{year}0503"])

            # 国庆节（10月1-7日）
            for day in range(1, 8):
                holidays.append(f"{year}10{day:02d}")

        return holidays

    async def cleanup(self):
        """清理资源"""
        if hasattr(self, 'executor'):
            self.executor.shutdown(wait=True)
        self.logger.info("AKShare提供者资源已清理")
